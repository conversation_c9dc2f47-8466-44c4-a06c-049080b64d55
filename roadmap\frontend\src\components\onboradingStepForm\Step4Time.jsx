import React from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
const Step4Time = ({ control }) => {
  return (
    <div>
      <FormField
        name="totalDuration"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Total Duration (weeks/months)</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="daysPerWeek"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Days per Week (1–7)</FormLabel>
            <FormControl>
              <Input type="number" min={1} max={7} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="hoursPerDay"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Hours per Day (1–12)</FormLabel>
            <FormControl>
              <Input type="number" min={1} max={12} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default Step4Time;
