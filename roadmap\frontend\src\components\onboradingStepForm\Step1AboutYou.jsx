import React from "react";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
const roles = [
  "Student",
  "Working professional",
  "Career switcher",
  "Hobbyist",
  "Other",
];
const Step1AboutYou = ({ control }) => {
  return (
    <div>
      <h1 className="text-white text-xl text-start font-semibold">
        Step 1 – About You
      </h1>
      <FormField
        name="currentRole"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Current Role</FormLabel>
            <FormControl>
              <div>
                <Select
                  value={
                    typeof watch === "function"
                      ? watch("currentRole")
                      : undefined
                  }
                  onValueChange={(value) =>
                    typeof setValue === "function"
                      ? setValue("currentRole", value)
                      : undefined
                  }
                >
                  <SelectTrigger>Current Role</SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Student">Student</SelectItem>
                    <SelectItem value="Working professional">
                      Working professional
                    </SelectItem>
                    <SelectItem value="Career switcher">
                      Career switcher
                    </SelectItem>
                    <SelectItem value="Hobbyist">Hobbyist</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors?.currentRole && (
                  <p className="text-red-500">{errors.currentRole.message}</p>
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="currentField"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Field/Industry (optional)</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="age"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Age Range (optional)</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default Step1AboutYou;
