import React from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem } from "@/components/ui/select";
const topicReasons = ["Career", "<PERSON><PERSON>", "Academic", "Side project", "Other"];
const Step2Topic = ({ control }) => {
  return (
    <div>
      <FormField
        name="topic"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Topic to Learn</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="topicReason"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Reason for Learning</FormLabel>
            <FormControl>
              <Select
                {...field}
                onValueChange={field.onChange}
                value={field.value || ""}
              >
                <SelectContent>
                  {topicReasons.map((reason) => (
                    <SelectItem key={reason} value={reason}>
                      {reason}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default Step2Topic;
