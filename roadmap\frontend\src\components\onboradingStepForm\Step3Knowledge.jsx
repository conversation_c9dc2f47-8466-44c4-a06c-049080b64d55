import React from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
const Step3Knowledge = ({ control }) => {
  return (
    <div>
      <FormField
        name="knowledgeLevel"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Knowledge Level (0–10)</FormLabel>
            <FormControl>
              <Input type="number" min={0} max={10} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="knowledgeDescription"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Describe What You Know</FormLabel>
            <FormControl>
              <Textarea {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default Step3Knowledge;
