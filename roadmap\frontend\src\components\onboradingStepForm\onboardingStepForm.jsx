import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { onboradingStepFormschema } from "@/components/onboradingStepForm/schema";

import { Form } from "@/components/ui/form";
import Step1<PERSON><PERSON>utYou from "./Step1AboutYou";
import Step2Topic from "./Step2Topic";
import Step3Knowledge from "./Step3Knowledge";
import Step4Time from "./Step4Time";

const OnboardingStepForm = () => {
  const [currentStep, setCurrentStep] = React.useState(0);
  const form = useForm({
    resolver: zodResolver(onboradingStepFormschema),
    mode: "onBlur",
  });

  const formSteps = [
    <Step1AboutYou key="step1" control={form.control} />,
    <Step2Topic key="step2" control={form.control} />,
    <Step3Knowledge key="step3" control={form.control} />,
    <Step4Time key="step4" control={form.control} />,
  ];

  return (
    <div className="w-full h-full border-2 border-slate-800 rounded-2xl p-2">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => console.log(data))}
          className="space-y-6 max-w-xl mx-auto p-6"
        >
          {formSteps[currentStep]}
          <div className="flex justify-between">
            <button
              type="button"
              className="bg-gray-600 text-white px-4 py-2 rounded"
              onClick={() => setCurrentStep(currentStep - 1)}
              disabled={currentStep === 0}
            >
              Previous
            </button>
            {currentStep === formSteps.length - 1 ? (
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded"
              >
                Submit
              </button>
            ) : (
              <button
                type="button"
                className="bg-gray-600 text-white px-4 py-2 rounded"
                onClick={() => setCurrentStep(currentStep + 1)}
                disabled={currentStep === formSteps.length - 1}
              >
                Next
              </button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
};

export default OnboardingStepForm;
